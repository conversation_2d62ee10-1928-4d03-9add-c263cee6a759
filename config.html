<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chatbot API Configuration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .model-config {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .model-name {
            font-size: 18px;
            font-weight: bold;
            color: #444;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .status.enabled {
            background-color: #d4edda;
            color: #155724;
        }
        .status.disabled {
            background-color: #f8d7da;
            color: #721c24;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        select {
            width: 100%;
            padding: 8px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.save-all {
            background-color: #2196F3;
            display: block;
            margin: 20px auto;
            padding: 12px 25px;
            font-size: 16px;
        }
        button.save-all:hover {
            background-color: #0b7dda;
        }
        .response-message {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .links {
            margin-top: 30px;
            text-align: center;
        }
        .links a {
            margin: 0 10px;
            color: #2196F3;
            text-decoration: none;
        }
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chatbot API Configuration</h1>
        
        <div id="statusMessage" class="response-message" style="display: none;"></div>
        
        <div id="configForms">
            <!-- Configuration forms will be added here -->
            <p>Loading configuration status...</p>
        </div>
        
        <button class="save-all" id="saveAllBtn">Save All Configuration</button>
        
        <div class="links">
            <a href="home.html">Go to Chatbot Interface</a>
            <a href="test.html">Test Server Connection</a>
            <a href="http://localhost:9000/status" target="_blank">View API Status</a>
        </div>
    </div>
    
    <script>
        // Model options
        const modelOptions = {
            gemini: ['gemini-1.5-flash', 'gemini-1.5-pro', 'gemini-1.0-pro'],
            chatgpt: ['gpt-4o', 'gpt-4', 'gpt-3.5-turbo'],
            claude: ['claude-3-5-sonnet-20240620', 'claude-3-opus-20240229', 'claude-3-haiku-20240307'],
            deepseek: ['deepseek-chat', 'deepseek-coder']
        };
        
        // Initialize the page
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // Fetch current configuration status
                const response = await fetch('http://localhost:9000/status');
                const data = await response.json();
                
                // Generate configuration forms for each model
                const configForms = document.getElementById('configForms');
                configForms.innerHTML = '';
                
                Object.entries(data.models).forEach(([model, status]) => {
                    const modelConfig = document.createElement('div');
                    modelConfig.className = 'model-config';
                    
                    // Create header with model name and status
                    const statusClass = status.enabled ? 'enabled' : 'disabled';
                    const statusText = status.enabled ? 'Enabled' : 'Disabled';
                    
                    modelConfig.innerHTML = `
                        <div class="model-header">
                            <div class="model-name">${model.charAt(0).toUpperCase() + model.slice(1)}</div>
                            <div class="status ${statusClass}">${statusText}</div>
                        </div>
                        
                        <label for="${model}-api-key">API Key:</label>
                        <input type="text" id="${model}-api-key" placeholder="Enter ${model} API key" 
                               value="${status.api_key_set ? '********' : ''}">
                        
                        <label for="${model}-model">Model:</label>
                        <select id="${model}-model">
                            ${generateModelOptions(model, status.model)}
                        </select>
                    `;
                    
                    configForms.appendChild(modelConfig);
                });
            } catch (error) {
                console.error('Error fetching configuration:', error);
                document.getElementById('configForms').innerHTML = `
                    <div class="error">
                        Error loading configuration. Make sure the server is running at http://localhost:9000
                    </div>
                `;
            }
            
            // Add event listener for the save button
            document.getElementById('saveAllBtn').addEventListener('click', saveAllConfig);
        });
        
        // Generate options for model dropdown
        function generateModelOptions(modelType, currentModel) {
            let options = '';
            
            if (modelOptions[modelType]) {
                modelOptions[modelType].forEach(model => {
                    const selected = model === currentModel ? 'selected' : '';
                    options += `<option value="${model}" ${selected}>${model}</option>`;
                });
            }
            
            return options;
        }
        
        // Save all configuration
        async function saveAllConfig() {
            const config = {};
            
            // Collect configuration for each model
            Object.keys(modelOptions).forEach(model => {
                const apiKey = document.getElementById(`${model}-api-key`).value;
                const modelName = document.getElementById(`${model}-model`).value;
                
                // Only include if API key is provided and not masked
                if (apiKey && apiKey !== '********') {
                    config[model] = {
                        api_key: apiKey,
                        model: modelName
                    };
                } else if (modelName) {
                    // If only model is changed but API key is not (it's still masked)
                    config[model] = {
                        model: modelName
                    };
                }
            });
            
            try {
                // Send configuration to server
                const response = await fetch('http://localhost:9000/configure', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                
                // Display status message
                const statusMessage = document.getElementById('statusMessage');
                if (result.status === 'Configuration updated') {
                    statusMessage.className = 'response-message success';
                    statusMessage.textContent = 'Configuration successfully updated!';
                } else {
                    statusMessage.className = 'response-message error';
                    statusMessage.textContent = result.message || 'Error updating configuration.';
                }
                statusMessage.style.display = 'block';
                
                // Reload the page after 2 seconds to show updated status
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
                
            } catch (error) {
                console.error('Error saving configuration:', error);
                const statusMessage = document.getElementById('statusMessage');
                statusMessage.className = 'response-message error';
                statusMessage.textContent = 'Error connecting to server. Make sure it is running.';
                statusMessage.style.display = 'block';
            }
        }
    </script>
</body>
</html>
