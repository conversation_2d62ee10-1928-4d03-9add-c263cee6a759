<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>'s Homepage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            width: 100%;
            background: url('https://i.gifer.com/g1Y5.gif') center center;
            background-size: 100% 100%;
            display: flex;
            color: white;
            position: relative;
        }
        .clock {
            position: fixed;
            top: 2rem;
            right: 2rem;
            font-size: 2rem;
            font-weight: bold;
            text-align: right;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
        }
        .container {
            padding: 1.5rem;
            margin-top: 2rem;
            margin-left: 2rem;
        }
        h2 {
            color: white;
            margin: 0 0 0.6rem 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            font-size: 1.1rem;
        }
        ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            display: flex;
            gap: 0.8rem;
            flex-wrap: wrap;
        }
        section {
            margin-bottom: 1.5rem;
            display: flex;
            flex-direction: column;
        }
        a {
            color: #ffffff;
            text-decoration: none;
            font-size: 1rem;
            padding: 0.4rem 0.8rem;
            border-radius: 5px;
            transition: all 0.3s ease;
            background-color: rgba(0, 122, 204, 0.3);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            white-space: nowrap;
        }
        a:hover {
            background-color: rgba(0, 122, 204, 0.6);
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
        }
        .motivation-text {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            text-align: right;
            font-size: 1.5rem;
            line-height: 1.8;
            color: white;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7),
                         -2px -2px 4px rgba(0, 0, 0, 0.7),
                         2px -2px 4px rgba(0, 0, 0, 0.7),
                         -2px 2px 4px rgba(0, 0, 0, 0.7);
            max-width: 800px;
            font-weight: bold;
        }
        .motivation-text p {
            margin: 0;
            letter-spacing: 0.5px;
        }
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
        .motivation-text p {
            animation: float 4s ease-in-out infinite;
            animation-delay: calc(var(--delay) * 1s);
        }
        .motivation-text p:nth-child(1) { --delay: 0; }
        .motivation-text p:nth-child(2) { --delay: 1; }
        .motivation-text p:nth-child(3) { --delay: 2; }
        .motivation-text p:nth-child(4) { --delay: 3; }
    </style>
</head>
<body>
    <div class="container">
        <!-- Previous sections remain exactly the same -->
        <section>
            <h2>Search Engines</h2>
            <ul>
                <li><a href="http://www.google.co.in">Google</a></li>
                <li><a href="https://chatgpt.com">ChatGPT</a></li>
                <li><a href="https://claude.ai/new">Claude</a></li>
            </ul>
        </section>

        <section>
            <h2>My Docs and Notes</h2>
            <ul>
                <li><a href="http://github.com">GitHub</a></li>
                <li><a href="https://mail.google.com/mail/u/0/#inbox">Gmail</a></li>
                <li><a href="https://mega.nz/fm">Mega</a></li>
                <li><a href="https://drive.google.com/drive/home">GDrive</a></li>
            </ul>
        </section>

        <section>
            <h2>Goal Rudders and Anchors</h2>
            <ul>
                <li><a href="https://studyglows.com/">StudyGlows</a></li>
            </ul>
        </section>

        <section>
            <h2>Cloud Infra</h2>
            <ul>
                <li><a href="https://eu-north-1.signin.aws.amazon.com">AWS</a></li>
                <li><a href="https://cloud.google.com">GCP</a></li>
                <li><a href="https://portal.azure.com/#home">Azure</a></li>
            </ul>
        </section>

        <section>
            <h2>Jobs</h2>
            <ul>
                <li><a href="http://linkedin.com">LinkedIn</a></li>
                <li><a href="http://naukri.com">Naukri</a></li>
            </ul>
        </section>

        <section>
            <h2>BattleTraining</h2>
            <ul>
                <li><a href="https://www.udemy.com/">Udemy</a></li>
                <li><a href="https://www.youtube.com">Youtube</a></li>
                <li><a href="https://coursehunter.net">CourseHunter</a></li>
                <li><a href="https://coursetrain.net">CourseTrain</a></li>
                <li><a href="https://codewithmosh.com">Mosh</a></li>
                <li><a href="https://www.durgasoftonline.com/">Durga Soft</a></li>
            </ul>
        </section>

        <section>
            <h2>Battlegrounds</h2>
            <ul>
                <li><a href="https://leetcode.com">LeetCode</a></li>
                <li><a href="https://www.codechef.com">CodeChef</a></li>
                <li><a href="https://www.hackerrank.com">Hackerrank</a></li>
                <li><a href="https://www.kaggle.com">Kaggle</a></li>
            </ul>
        </section>

        <section>
            <h2>Messaging</h2>
            <ul>
                <li><a href="https://web.whatsapp.com">Whatsapp</a></li>
                <li><a href="https://web.telegram.org">Telegram</a></li>
                <li><a href="https://meet.goto.com/851801557">GoTo</a></li>
            </ul>
        </section>

        <section>
            <h2>Entertainment</h2>
            <ul>
                <li><a href="https://www.youtube.com">Youtube</a></li>
                <li><a href="https://music.youtube.com">Youtube Music</a></li>
            </ul>
        </section>
    </div>

    <div class="motivation-text">
        <p>Focus on only one goal, live and breathe for it.</p>
        <p>A great goal costs great sacrifice.</p>
        <p>Choose to work towards the goal, for work and success is a choice.</p>
        <p>Empty your mind, heart and just work towards the greater goal.</p>
    </div>
    
    <div class="clock" id="clock"></div>
    
    <script>
        function updateClock() {
            const now = new Date();
            const options = { 
                timeZone: 'Asia/Kolkata',
                hour: '2-digit', 
                minute: '2-digit', 
                second: '2-digit',
                hour12: true,
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            };
            
            const timeString = now.toLocaleString('en-IN', options);
            document.getElementById('clock').innerHTML = timeString;
        }
        
        // Update clock every second
        setInterval(updateClock, 1000);
        
        // Initial call to display clock immediately
        updateClock();
    </script>
</body>
</html>