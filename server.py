from fastapi import <PERSON><PERSON><PERSON>, Request
import httpx
import uvicorn
import json
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
from typing import Dict, Any, Optional

app = FastAPI()

# Enable CORS for all origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

@app.get("/")
async def root():
    return JSONResponse({"status": "Server is running", "message": "Use the /gemini endpoint for chat"})

@app.get("/test")
async def test():
    return JSONResponse({"status": "Test successful", "message": "Server is responding to GET requests"})

# API configuration for all chatbots
API_CONFIG = {
    "gemini": {
        "api_key": "AIzaSyCjZ5ds2DI7YbKnC4xNMxKRYG8caUNNsoQ",
        "api_url": "https://generativelanguage.googleapis.com/v1/models/gemini-1.5-flash:generateContent",
        "enabled": True
    },
    "chatgpt": {
        "api_key": "",  # To be filled when provided
        "api_url": "https://api.openai.com/v1/chat/completions",
        "model": "gpt-4o",  # Default model, can be updated
        "enabled": False
    },
    "claude": {
        "api_key": "",  # To be filled when provided
        "api_url": "https://api.anthropic.com/v1/messages",
        "model": "claude-3-5-sonnet-20240620",  # Default model, can be updated
        "enabled": False
    },
    "deepseek": {
        "api_key": "",  # To be filled when provided
        "api_url": "https://api.deepseek.com/v1/chat/completions",
        "model": "deepseek-chat",  # Default model, can be updated
        "enabled": False
    }
}

@app.post("/gemini")
async def gemini(request: Request):
    data = await request.json()
    message = data.get("message")
    if message:
        response = await call_ai_api("gemini", message)
        return {"response": response}
    return {"response": "No message received"}

async def call_ai_api(model: str, message: str) -> str:
    """Generic function to call any AI API based on the model name"""
    
    if model not in API_CONFIG:
        return f"Error: Unknown model '{model}'"
    
    config = API_CONFIG[model]
    
    if not config["enabled"] or not config["api_key"]:
        return f"Error: {model.capitalize()} API is not configured or enabled"
    
    try:
        async with httpx.AsyncClient() as client:
            # Prepare headers and payload based on the model
            if model == "gemini":
                headers = {}
                payload = {
                    "contents": [
                        {
                            "role": "user",
                            "parts": [
                                {"text": message}
                            ]
                        }
                    ],
                    "generationConfig": {
                        "temperature": 0.7,
                        "maxOutputTokens": 2048
                    }
                }
                url = f"{config['api_url']}?key={config['api_key']}"
            
            elif model == "chatgpt":
                headers = {
                    "Authorization": f"Bearer {config['api_key']}",
                    "Content-Type": "application/json"
                }
                payload = {
                    "model": config["model"],
                    "messages": [
                        {"role": "user", "content": message}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 2048
                }
                url = config["api_url"]
                
            elif model == "claude":
                headers = {
                    "x-api-key": config["api_key"],
                    "anthropic-version": "2023-06-01",
                    "Content-Type": "application/json"
                }
                payload = {
                    "model": config["model"],
                    "messages": [
                        {"role": "user", "content": message}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 2048
                }
                url = config["api_url"]
                
            elif model == "deepseek":
                headers = {
                    "Authorization": f"Bearer {config['api_key']}",
                    "Content-Type": "application/json"
                }
                payload = {
                    "model": config["model"],
                    "messages": [
                        {"role": "user", "content": message}
                    ],
                    "temperature": 0.7,
                    "max_tokens": 2048
                }
                url = config["api_url"]
            
            # Make the API call
            print(f"Calling {model.capitalize()} API with message: {message[:50]}..." if len(message) > 50 else message)
            
            response = await client.post(
                url,
                headers=headers,
                json=payload,
                timeout=30.0
            )
            
            # Log response details for debugging
            print(f"{model.capitalize()} API response status: {response.status_code}")
            response_text = response.text
            print(f"{model.capitalize()} API raw response: {response_text[:200]}..." if len(response_text) > 200 else response_text)
            
            # Process the response based on the model
            if response.status_code == 200:
                response_data = response.json()
                
                # Extract text based on the model's response format
                if model == "gemini":
                    if "candidates" in response_data and response_data["candidates"]:
                        return response_data["candidates"][0]["content"]["parts"][0]["text"]
                
                elif model == "chatgpt":
                    if "choices" in response_data and response_data["choices"]:
                        return response_data["choices"][0]["message"]["content"]
                
                elif model == "claude":
                    if "content" in response_data and response_data["content"]:
                        return response_data["content"][0]["text"]
                
                elif model == "deepseek":
                    if "choices" in response_data and response_data["choices"]:
                        return response_data["choices"][0]["message"]["content"]
                
                # If we couldn't extract the text, return the raw response for debugging
                print(f"Couldn't parse {model} response: {json.dumps(response_data)}")
                return f"Error parsing {model.capitalize()} response. Please check server logs."
            else:
                return f"Error calling {model.capitalize()} API: {response.status_code}. {response.text}"
    
    except Exception as e:
        print(f"Exception in {model} API call: {str(e)}")
        return f"Server error calling {model.capitalize()} API: {str(e)}"

@app.post("/chatgpt")
async def chatgpt(request: Request):
    data = await request.json()
    message = data.get("message")
    if message:
        if API_CONFIG["chatgpt"]["enabled"]:
            response = await call_ai_api("chatgpt", message)
            return {"response": response}
        return {"response": "ChatGPT API is not configured yet. Please provide an API key."}
    return {"response": "No message received"}

@app.post("/deepseek")
async def deepseek(request: Request):
    data = await request.json()
    message = data.get("message")
    if message:
        if API_CONFIG["deepseek"]["enabled"]:
            response = await call_ai_api("deepseek", message)
            return {"response": response}
        return {"response": "DeepSeek API is not configured yet. Please provide an API key."}
    return {"response": "No message received"}

@app.post("/claude")
async def claude(request: Request):
    data = await request.json()
    message = data.get("message")
    if message:
        if API_CONFIG["claude"]["enabled"]:
            response = await call_ai_api("claude", message)
            return {"response": response}
        return {"response": "Claude API is not configured yet. Please provide an API key."}
    return {"response": "No message received"}

@app.post("/configure")
async def configure_api(request: Request):
    """Endpoint to configure API keys for the different chatbots"""
    try:
        data = await request.json()
        
        # Update configurations for each provided model
        for model, config in data.items():
            if model in API_CONFIG:
                # Check if api_key is provided
                if "api_key" in config and config["api_key"]:
                    API_CONFIG[model]["api_key"] = config["api_key"]
                    API_CONFIG[model]["enabled"] = True
                    
                # Check if model name is provided (for models that have different options)
                if "model" in config and config["model"]:
                    API_CONFIG[model]["model"] = config["model"]
                    
                # Check if api_url is provided
                if "api_url" in config and config["api_url"]:
                    API_CONFIG[model]["api_url"] = config["api_url"]
                    
                print(f"Updated configuration for {model}")
        
        # Return current configuration status (without showing the actual API keys)
        status = {}
        for model, config in API_CONFIG.items():
            status[model] = {
                "enabled": config["enabled"],
                "model": config.get("model", ""),
                "api_key_set": bool(config["api_key"])
            }
            
        return {"status": "Configuration updated", "models": status}
    
    except Exception as e:
        print(f"Error in configure_api: {str(e)}")
        return {"status": "error", "message": f"Failed to update configuration: {str(e)}"}

@app.get("/status")
async def get_status():
    """Get the current configuration status"""
    status = {}
    for model, config in API_CONFIG.items():
        status[model] = {
            "enabled": config["enabled"],
            "model": config.get("model", ""),
            "api_key_set": bool(config["api_key"])
        }
        
    return {"models": status}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=9000)