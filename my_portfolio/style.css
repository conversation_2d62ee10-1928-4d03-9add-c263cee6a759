body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
    color: #333;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    overflow-x: hidden; /* Prevent horizontal scrollbars if canvas is too wide initially */
}

header {
    background-color: #3498db; /* New color */
    color: white;
    padding: 1em 0;
    text-align: center;
    width: 100%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

header h1 {
    margin: 0;
    font-size: 1.8em;
}

main {
    flex: 1;
    width: 100%; /* Full width for main content area */
    display: flex; /* Use flexbox for layout */
    flex-direction: column; /* Stack children vertically */
    align-items: center; /* Center children horizontally */
    padding: 10px; /* Add some padding */
    box-sizing: border-box;
}

#info-panel {
    width: 90%;
    max-width: 800px;
    background-color: #ecf0f1;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    text-align: center;
}

#route-selection h2 {
    margin-top: 0;
    color: #2c3e50;
}

#route-selection button {
    background-color: #2980b9;
    color: white;
    border: none;
    padding: 10px 15px;
    margin: 5px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.3s;
}

#route-selection button:hover {
    background-color: #1f638f;
}

#route-selection button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

#controls-info p {
    font-style: italic;
    color: #555;
    margin: 10px 0;
    font-size: 1.1em;
}

#features-info {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    border-left: 4px solid #3498db;
}

#features-info h3 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 1.2em;
}

#features-info ul {
    list-style: none;
    padding-left: 0;
    margin: 10px 0;
}

#features-info li {
    padding: 5px 0;
    color: #34495e;
    font-size: 0.95em;
    border-bottom: 1px solid #ecf0f1;
}

#features-info li:last-child {
    border-bottom: none;
}

#start-game-btn {
    background-color: #e67e22; /* Orange color */
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1.1em;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
    margin-top: 10px;
}

#start-game-btn.hidden {
    display: none;
}

#start-game-btn:hover {
    background-color: #d35400;
}


#game-canvas-container {
    width: 95%; /* Responsive width */
    max-width: 1200px; /* Max width for larger screens */
    height: 60vh; /* Responsive height */
    min-height: 400px; /* Minimum height */
    margin: 0 auto; /* Center the canvas container */
    background-color: #2c3e50; /* Dark background for canvas */
    border: 3px solid #1a252f;
    border-radius: 8px;
    overflow: hidden; /* Important for Three.js canvas */
    position: relative; /* For potential overlays or UI elements on canvas */
}

/* Ensure canvas itself fills the container */
#game-canvas-container canvas {
    display: block; /* Removes extra space below canvas */
    width: 100% !important; /* Override Three.js inline styles if necessary */
    height: 100% !important;
}

/* Car HUD Overlay */
#car-hud {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 100;
    display: flex;
    flex-direction: column;
    gap: 15px;
    pointer-events: none;
}

#speedometer {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    text-align: center;
    min-width: 80px;
    border: 2px solid #3498db;
}

#position-indicator {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    text-align: center;
    min-width: 80px;
    border: 2px solid #27ae60;
}

#camera-indicator {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    text-align: center;
    min-width: 80px;
    border: 2px solid #9b59b6;
}

#camera-mode {
    font-size: 1.1em;
    color: #9b59b6;
    margin: 2px 0;
    font-weight: bold;
    text-transform: capitalize;
}

#position-x, #position-z {
    font-size: 0.9em;
    color: #27ae60;
    margin: 2px 0;
    font-family: monospace;
}

.hud-note {
    font-size: 0.7em;
    color: #95a5a6;
    margin-top: 5px;
}

#speed-value {
    font-size: 2em;
    font-weight: bold;
    color: #3498db;
    margin: 5px 0;
}

.hud-label {
    font-size: 0.8em;
    color: #bdc3c7;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hud-unit {
    font-size: 0.9em;
    color: #95a5a6;
}

#controls-reminder {
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 10px;
    border-radius: 8px;
    font-size: 0.8em;
    border: 1px solid #34495e;
}

.control-key {
    margin: 2px 0;
    color: #ecf0f1;
    font-family: monospace;
}


#achievement-display {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

#achievement-display.hidden {
    display: none;
}

#achievement-content {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 0 15px rgba(0,0,0,0.3);
    max-width: 80%;
    width: 500px;
}

#achievement-content h2 {
    color: #4CAF50;
    margin-top: 0;
}

#achievement-content p {
    font-size: 1.1em;
    line-height: 1.6;
}

#close-achievement {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    margin-top: 20px;
}

#close-achievement:hover {
    background-color: #45a049;
}

#controls {
    text-align: center;
    margin-top: 20px;
}

#controls p {
    font-style: italic;
    color: #555;
}

#start-game-btn {
    background-color: #ff9800;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.2em;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#start-game-btn:hover {
    background-color: #f57c00;
}

footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 1em 0;
    width: 100%;
    margin-top: auto; /* Pushes footer to the bottom */
}
