<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My 3D Portfolio Adventure</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <header>
        <h1>My 3D Portfolio Adventure</h1>
    </header>

    <main>
        <div id="info-panel">
            <!-- Route selection removed for free-roam -->
            <div id="controls-info">
                <h2>🚗 Enhanced Portfolio Adventure</h2>
                <p>Experience improved car physics and stunning visuals! Use W, A, S, D or Arrow Keys to drive.</p>
                <div id="features-info">
                    <h3>✨ Fixed Issues & Features:</h3>
                    <ul>
                        <li>🔧 <strong>FIXED:</strong> Ground-only Y-axis rotation (no more 3D tilting)</li>
                        <li>🔧 <strong>FIXED:</strong> Completely unrestricted movement across entire map</li>
                        <li>🎯 Realistic car physics with acceleration & momentum</li>
                        <li>🎨 Enhanced 3D car model with metallic paint & rims</li>
                        <li>🌪️ Dynamic particle effects and dust clouds</li>
                        <li>🎮 Smooth steering (works even when stationary)</li>
                        <li>📷 Improved camera following system</li>
                        <li>🌍 Full 360° Y-axis rotation on flat ground plane</li>
                        <li>🗺️ Can reach all four corners: (±250, ±250)</li>
                    </ul>
                </div>
            </div>
        </div>

        <div id="game-canvas-container">
            <!-- 3D Canvas will be inserted here by Three.js -->
            <div id="car-hud">
                <div id="speedometer">
                    <div class="hud-label">Speed</div>
                    <div id="speed-value">0</div>
                    <div class="hud-unit">km/h</div>
                </div>
                <div id="position-indicator">
                    <div class="hud-label">Position</div>
                    <div id="position-x">X: 0</div>
                    <div id="position-z">Z: 0</div>
                    <div class="hud-note">Map: ±250 units</div>
                </div>
                <div id="controls-reminder">
                    <div class="hud-label">Controls</div>
                    <div class="control-key">W/↑ - Forward</div>
                    <div class="control-key">S/↓ - Reverse</div>
                    <div class="control-key">A/← - Turn Left</div>
                    <div class="control-key">D/→ - Turn Right</div>
                </div>
            </div>
        </div>

        <div id="achievement-display" class="hidden">
            <div id="achievement-content">
                <h2 id="achievement-title"></h2>
                <p id="achievement-description"></p>
                <button id="close-achievement">Continue Journey</button>
            </div>
        </div>
    </main>

    <footer>
        <p>&copy; 2025 Your Name - Portfolio</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>
