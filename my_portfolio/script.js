document.addEventListener('DOMContentLoaded', () => {
    const achievementDisplay = document.getElementById('achievement-display');
    const achievementTitleElem = document.getElementById('achievement-title');
    const achievementDescriptionElem = document.getElementById('achievement-description');
    const closeAchievementBtn = document.getElementById('close-achievement');
    const gameCanvasContainer = document.getElementById('game-canvas-container');
    const controlsInfoPanel = document.getElementById('controls-info');

    const achievements = { /* ... (achievements data remains the same) ... */ 
        "achievement1": { title: "Project Alpha Completed", description: "Successfully launched Project Alpha, exceeding all initial targets." },
        "achievement2": { title: "Innovation Award 2023", description: "Received the company's annual Innovation Award for developing a groundbreaking new feature." },
        "achievement3": { title: "Led a Successful Team", description: "Mentored and led a team of 5 to deliver a critical project on time and under budget." },
        "achievement4": { title: "Published Research Paper", description: "Co-authored and published a research paper in a renowned industry journal." },
        "achievement5": { title: "Client Acclaim", description: "Received outstanding feedback from a major client for exceptional problem-solving skills." },
        "achievement6": { title: "Community Contribution", description: "Volunteered expertise to a local non-profit, helping them streamline their tech operations." },
        "achievement7": { title: "Certified Cloud Architect", description: "Achieved professional certification as a Cloud Solutions Architect, demonstrating expertise in cloud infrastructure." },
        "achievement8": { title: "Open Source Contributor", description: "Made significant contributions to a popular open-source project, enhancing its functionality." },
        "achievement9": { title: "Speaker at Tech Conference", description: "Presented a talk on emerging technologies at a national tech conference." },
        "achievement10": { title: "Patent for Novel Algorithm", description: "Awarded a patent for inventing a novel algorithm that improves data processing efficiency." },
        "achievement11": { title: "Process Optimization Expert", description: "Redesigned a key business process, resulting in a 30% reduction in operational costs." }
    };
    const allMilestonesData = [ /* ... (milestones data remains the same) ... */ 
        { id: "m1", position: new THREE.Vector3(50, 0, 0), achievementId: "achievement1", reached: false },
        { id: "m2", position: new THREE.Vector3(100, 0, 20), achievementId: "achievement2", reached: false },
        { id: "m3", position: new THREE.Vector3(150, 0, -10), achievementId: "achievement10", reached: false },
        { id: "m4", position: new THREE.Vector3(0, 0, 50), achievementId: "achievement3", reached: false },
        { id: "m5", position: new THREE.Vector3(-30, 0, 100), achievementId: "achievement5", reached: false },
        { id: "m6", position: new THREE.Vector3(20, 0, 150), achievementId: "achievement11", reached: false },
        { id: "m7", position: new THREE.Vector3(-50, 0, -50), achievementId: "achievement7", reached: false },
        { id: "m8", position: new THREE.Vector3(-100, 0, 0), achievementId: "achievement8", reached: false },
        { id: "m9", position: new THREE.Vector3(-150, 0, 50), achievementId: "achievement9", reached: false },
        { id: "m10", position: new THREE.Vector3(-100, 0, 100), achievementId: "achievement4", reached: false },
        { id: "m11", position: new THREE.Vector3(70, 0, -60), achievementId: "achievement6", reached: false },
    ];
    let milestoneObjects = [];
    let gameActive = false;
    let scene, camera, renderer, carGroup, wheels = []; // carBody removed for now
    const clock = new THREE.Clock();
    const carSpeed = 18;
    const turnSpeed = Math.PI * 1.25; // Increased turn speed again (was Math.PI)
    const keysPressed = {};
    const wheelRadius = 0.4;
    const wheelRotationSpeed = carSpeed / wheelRadius;
    const initialCarPosition = new THREE.Vector3(0, wheelRadius, 0);
    const initialCarLookAt = new THREE.Vector3(0, wheelRadius, -1);

    function initThreeJS() {
        console.log("initThreeJS called");
        if (!gameCanvasContainer) {
            console.error("gameCanvasContainer not found!");
            return;
        }
        console.log("gameCanvasContainer dimensions:", gameCanvasContainer.clientWidth, gameCanvasContainer.clientHeight);

        scene = new THREE.Scene();
        scene.background = new THREE.Color(0x70c5ce); // Sky blue (restored)

        let aspect = gameCanvasContainer.clientWidth / gameCanvasContainer.clientHeight;
        if (!isFinite(aspect) || aspect <= 0) {
            console.warn("Calculated aspect ratio is invalid, falling back to window aspect. Container dimensions:", gameCanvasContainer.clientWidth, gameCanvasContainer.clientHeight);
            aspect = window.innerWidth / window.innerHeight;
            if (!isFinite(aspect) || aspect <= 0) { // Fallback if window dimensions are also zero
                aspect = 16/9;
                console.warn("Window aspect also invalid, defaulting to 16/9");
            }
        }
        camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
        camera.position.set(0, 5, 10); // Position to see the origin
        camera.lookAt(0, 0, 0);
        console.log("Camera initialized");

        renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(gameCanvasContainer.clientWidth, gameCanvasContainer.clientHeight);
        renderer.shadowMap.enabled = true; // Restore shadow map
        gameCanvasContainer.appendChild(renderer.domElement);
        console.log("Renderer initialized and appended");

        // Ground (restoring from previous full version)
        const groundGeometry = new THREE.PlaneGeometry(500, 500);
        const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x556B2F, side: THREE.DoubleSide });
        const groundMesh = new THREE.Mesh(groundGeometry, groundMaterial);
        groundMesh.rotation.x = -Math.PI / 2;
        groundMesh.receiveShadow = true;
        scene.add(groundMesh);
        console.log("Ground added to scene");

        // Restore car creation
        createCar();
        console.log("createCar called");
        
        
        // Milestones still commented out for now
        setupAllMilestoneObjects(); // Re-enable milestone creation
        console.log("setupAllMilestoneObjects called");

        // Restore full lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.7); // Restored intensity
        scene.add(ambientLight);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.9); // Restored intensity
        directionalLight.position.set(15, 25, 10);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 1024;
        directionalLight.shadow.mapSize.height = 1024;
        scene.add(directionalLight);
        console.log("Full lighting restored");


        window.addEventListener('resize', onWindowResize, false);
        startGame(); // Restore starting the game logic
        animate();
        console.log("initThreeJS finished, animation loop started");
    }

    function createCar() { /* ... (function remains the same but is not called initially) ... */ 
        carGroup = new THREE.Group();
        carGroup.position.copy(initialCarPosition);
        carGroup.lookAt(initialCarLookAt);

        const bodyGeometry = new THREE.BoxGeometry(2, 1, 4);
        const bodyMaterial = new THREE.MeshStandardMaterial({ color: 0xff0000 });
        const carBody = new THREE.Mesh(bodyGeometry, bodyMaterial); // Re-declare carBody if used
        carBody.castShadow = true;
        carBody.position.y = 0.5;
        carGroup.add(carBody);

        const wheelGeometry = new THREE.CylinderGeometry(wheelRadius, wheelRadius, 0.3, 32);
        const wheelMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
        const wheelPositions = [
            new THREE.Vector3(1.1, 0, 1.5), new THREE.Vector3(-1.1, 0, 1.5),
            new THREE.Vector3(1.1, 0, -1.5), new THREE.Vector3(-1.1, 0, -1.5)
        ];
        wheels = [];
        for (let i = 0; i < 4; i++) {
            const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
            wheel.castShadow = true;
            wheel.rotation.z = Math.PI / 2;
            wheel.position.copy(wheelPositions[i]);
            wheel.position.y = 0;
            carGroup.add(wheel);
            wheels.push(wheel);
        }
        scene.add(carGroup);
    }

    function onWindowResize() { /* ... (function remains the same) ... */ 
        if (!renderer || !camera || !gameCanvasContainer) return;
        const newWidth = gameCanvasContainer.clientWidth;
        const newHeight = gameCanvasContainer.clientHeight;

        let aspect = newWidth / newHeight;
        if (!isFinite(aspect) || aspect <= 0) {
            console.warn("Calculated aspect ratio during resize is invalid, falling back to window aspect. Container dimensions:", newWidth, newHeight);
            aspect = window.innerWidth / window.innerHeight;
             if (!isFinite(aspect) || aspect <= 0) { 
                aspect = 16/9;
                console.warn("Window aspect also invalid during resize, defaulting to 16/9");
            }
        }
        camera.aspect = aspect;
        camera.updateProjectionMatrix();
        renderer.setSize(newWidth, newHeight);
    }

    function setupAllMilestoneObjects() { /* ... (function remains the same but is not called initially) ... */ 
        milestoneObjects.forEach(obj => scene.remove(obj)); 
        milestoneObjects = [];
        allMilestonesData.forEach(milestoneData => {
            if (!milestoneData.reached) { 
                const milestoneGeometry = new THREE.CylinderGeometry(1.2, 1.2, 5, 16);
                const milestoneMaterial = new THREE.MeshPhongMaterial({ color: 0xffd700, emissive: 0x443300 });
                const milestoneMesh = new THREE.Mesh(milestoneGeometry, milestoneMaterial);
                milestoneMesh.position.copy(milestoneData.position);
                milestoneMesh.position.y = 2.5; 
                milestoneMesh.castShadow = true;
                scene.add(milestoneMesh);
                milestoneObjects.push(milestoneMesh); 
                milestoneData.object = milestoneMesh; 
            }
        });
    }

    function showAchievement(achievementId) { /* ... (function remains the same) ... */ 
        gameActive = false; 
        const achievement = achievements[achievementId];
        if (achievement) {
            achievementTitleElem.textContent = achievement.title;
            achievementDescriptionElem.textContent = achievement.description;
            achievementDisplay.classList.remove('hidden');
        }
    }
    function hideAchievement() { /* ... (function remains the same) ... */ 
        achievementDisplay.classList.add('hidden');
        gameActive = true; 
    }
    function startGame() { /* ... (function remains the same but is not called initially for full game logic) ... */
        gameActive = true;
        const pTag = controlsInfoPanel ? controlsInfoPanel.querySelector('p') : null;
        if (pTag) {
             pTag.textContent = "Driving... Use W/S (Forward/Backward), A/D (Turn Left/Right) or Arrow Keys.";
        } else if (controlsInfoPanel) {
            console.warn("Could not find 'p' tag within #controls-info to update text. Check HTML structure.");
        } else {
            console.warn("#controls-info panel not found. Check HTML ID.");
        }
        console.log("Game active - Free Roam!");
    }

    function updateCar(deltaTime) { /* ... (function remains the same but is not called if car not created) ... */ 
        if (!gameActive || !carGroup || wheels.length === 0) return; // Check wheels too

        const moveDistance = carSpeed * deltaTime;
        const rotateAngle = turnSpeed * deltaTime;

        if (keysPressed['w'] || keysPressed['arrowup']) {
            carGroup.translateZ(-moveDistance);
            wheels.forEach(wheel => wheel.rotateX(-wheelRotationSpeed * deltaTime));
        }
        if (keysPressed['s'] || keysPressed['arrowdown']) {
            carGroup.translateZ(moveDistance);
            wheels.forEach(wheel => wheel.rotateX(wheelRotationSpeed * deltaTime));
        }
        if (keysPressed['a'] || keysPressed['arrowleft']) {
            carGroup.rotateY(rotateAngle);
        }
        if (keysPressed['d'] || keysPressed['arrowright']) {
            carGroup.rotateY(-rotateAngle);
        }

        carGroup.position.y = wheelRadius;
        carGroup.rotation.x = 0;
        carGroup.rotation.z = 0;

        console.log("Car Rotation Y:", carGroup.rotation.y); // For debugging turning - UNCOMMENTED

        const cameraLookAtOffset = new THREE.Vector3(0, 2, 0);
        const lookAtPoint = carGroup.position.clone().add(cameraLookAtOffset);
        const relativeCameraPosition = new THREE.Vector3(0, 5, 12);
        const desiredCameraPosition = relativeCameraPosition.applyMatrix4(carGroup.matrixWorld);
        camera.position.lerp(desiredCameraPosition, 0.1);
        const minCameraHeight = wheelRadius + 1.5;
        if (camera.position.y < minCameraHeight) {
            camera.position.y = minCameraHeight;
        }
        camera.lookAt(lookAtPoint);

        checkMilestonesCollision();
    }

    function checkMilestonesCollision() { 
        if (!gameActive) return;
        allMilestonesData.forEach(milestoneData => {
            // Ensure carGroup and milestoneData.object exist before checking distance
            if (!milestoneData.reached && milestoneData.object && carGroup && carGroup.position.distanceTo(milestoneData.position) < 6) {
                milestoneData.reached = true;
                showAchievement(milestoneData.achievementId);
                scene.remove(milestoneData.object); 
                const index = milestoneObjects.indexOf(milestoneData.object);
                if (index > -1) milestoneObjects.splice(index, 1);
                delete milestoneData.object; 
            }
        });
    }
    
    closeAchievementBtn.addEventListener('click', hideAchievement);
    document.addEventListener('keydown', (event) => { 
        keysPressed[event.key.toLowerCase()] = true; 
        // Prevent default for game keys to avoid page scroll
        if (gameActive && ['w', 'a', 's', 'd', 'arrowup', 'arrowdown', 'arrowleft', 'arrowright'].includes(event.key.toLowerCase())) {
            event.preventDefault();
        }
    });
    document.addEventListener('keyup', (event) => { 
        keysPressed[event.key.toLowerCase()] = false; 
    });

    function animate() {
        requestAnimationFrame(animate);
        const deltaTime = clock.getDelta(); 
        updateCar(deltaTime); // Restore car update
        
        if (renderer && scene && camera) {
            renderer.render(scene, camera);
        } else {
            console.error("Renderer, scene or camera is missing in animate loop");
        }
    }

    initThreeJS();
});
