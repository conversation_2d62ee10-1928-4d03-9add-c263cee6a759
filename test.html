<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Server Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Server Connection Test</h1>
        <p>This page tests the connection to your local FastAPI server.</p>
        
        <button id="testGet">Test GET Request</button>
        <button id="testGemini">Test Gemini POST Request</button>
        
        <div id="result">
            <p>Results will appear here...</p>
        </div>
    </div>

    <script>
        const resultDiv = document.getElementById('result');
        
        // Test GET request
        document.getElementById('testGet').addEventListener('click', async () => {
            resultDiv.innerHTML = '<p>Sending GET request to server...</p>';
            try {
                const response = await fetch('http://localhost:9000/test');
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h3>GET Request Successful!</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error!</h3>
                    <p>Could not connect to the server. Make sure it's running at http://localhost:9000</p>
                    <p>Error details: ${error.message}</p>
                `;
                console.error('Error:', error);
            }
        });
        
        // Test Gemini POST request
        document.getElementById('testGemini').addEventListener('click', async () => {
            resultDiv.innerHTML = '<p>Sending POST request to Gemini endpoint...</p>';
            try {
                const response = await fetch('http://localhost:9000/gemini', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message: "Hello, this is a test message" })
                });
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h3>POST Request Successful!</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error!</h3>
                    <p>Failed to send POST request to the Gemini endpoint.</p>
                    <p>Error details: ${error.message}</p>
                `;
                console.error('Error:', error);
            }
        });
    </script>
</body>
</html>
